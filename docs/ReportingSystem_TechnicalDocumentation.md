# Akademik Performans Raporlama Sistemi - Teknik Dokümantasyon

## İçindekiler
1. [<PERSON><PERSON><PERSON>](#1-sistem-genel-bakış)
2. [<PERSON><PERSON> (Business Workflows)](#2-iş-akışları-business-workflows)
3. [<PERSON><PERSON> Akışı (Data Flow)](#3-veri-akışı-data-flow)
4. [<PERSON>sa<PERSON>lama Metodolojileri](#4-hesaplama-metodolojileri)
5. [<PERSON><PERSON>](#5-rapor-türleri)
6. [Kullanıcı Rolleri ve Yetkilendirme](#6-kullanıcı-rolleri-ve-yetkilendirme)
7. [Performans ve Optimizasyon](#7-performans-ve-optimizasyon)
8. [Güvenlik](#8-güven<PERSON>)

---

## 1. Sistem Genel Bakış

### 1.1 Amaç ve Kapsam

Akademik Performans Raporlama Si<PERSON>mi, akademik personelin performansını çok boyutlu olarak <PERSON>en, analiz eden ve raporlayan kapsamlı bir sistemdir. <PERSON><PERSON><PERSON>, objektif veriler kullanarak akademisyenlerin güçlü yönlerini ve gelişim alanlarını belirler.

**Ana Hedefler:**
- Akademik personelin performansının sistematik değerlendirilmesi
- Kategori ve kriter bazında detaylı performans analizi
- Karşılaştırmalı performans raporlaması (bölüm, fakülte, üniversite)
- Trend analizi ve zaman bazında performans takibi
- Çoklu format rapor üretimi (PDF, Excel, CSV)
- Karar destek sistemleri için veri sağlama

### 1.2 Sistemin Organizasyon İçindeki Rolü

Raporlama sistemi, akademik kurumların performans yönetiminde merkezi bir rol oynar:

- **Stratejik Planlama**: Kurumsal hedeflere uygun performans stratejileri geliştirme
- **Performans İzleme**: Akademik personelin sürekli performans takibi
- **Karar Destek**: Terfi, ödül ve gelişim kararlarında objektif veri desteği
- **Kalite Güvence**: Akademik kalite standartlarının sürdürülmesi ve iyileştirilmesi
- **Benchmarking**: Departmanlar arası ve bireysel performans karşılaştırmaları
- **Sürekli İyileştirme**: Performans trendleri analizi ile gelişim alanlarının belirlenmesi

### 1.3 Ana Bileşenler

#### 1.3.1 Raporlama Motoru (ReportingManager)
- Performans hesaplama algoritmaları
- Rapor oluşturma ve formatlama
- Veri aggregasyonu ve analiz

#### 1.3.2 Veri Erişim Katmanı (ReportingStore)
- Performans verilerinin toplanması
- Kategori ve kriter skorlarının hesaplanması
- İstatistiksel analizler

#### 1.3.3 API Katmanı (ReportingController)
- RESTful endpoint'ler
- Yetkilendirme ve güvenlik
- Request/Response yönetimi

#### 1.3.4 Cache Sistemi
- Performans optimizasyonu
- Sık kullanılan verilerin önbelleklenmesi
- Sistem yükünün azaltılması

---

## 2. İş Akışları (Business Workflows)

### 2.1 Akademisyen Performans Raporu Oluşturma

```mermaid
flowchart TD
    A[Rapor Talebi] --> B[Kullanıcı Yetki Kontrolü]
    B --> C{Yetki Var mı?}
    C -->|Hayır| D[Yetki Hatası]
    C -->|Evet| E[Filtreleme Kriterlerini Al]
    E --> F[Akademisyen Verilerini Getir]
    F --> G[Performans Verilerini Hesapla]
    G --> H[Kategori Skorlarını Hesapla]
    H --> I[Kriter Skorlarını Hesapla]
    I --> J[Feedback İstatistiklerini Hesapla]
    J --> K[Genel Performans Skorunu Hesapla]
    K --> L[Performans Seviyesini Belirle]
    L --> M[Raporu Formatla]
    M --> N[Cache'e Kaydet]
    N --> O[Raporu Döndür]
```

**Adımlar:**
1. **Yetkilendirme**: Kullanıcının raporlama yetkisi kontrolü
2. **Veri Toplama**: Akademisyen profil ve performans verilerinin toplanması
3. **Hesaplama**: Kategori ve kriter bazında skor hesaplamaları
4. **Analiz**: İstatistiksel analizler ve trend hesaplamaları
5. **Formatlama**: Rapor formatına göre veri düzenlenmesi
6. **Önbellekleme**: Performans için cache'e kaydetme
7. **Sunum**: Kullanıcıya rapor sunumu

### 2.2 Çoklu Akademisyen Raporu Oluşturma

```mermaid
flowchart TD
    A[Çoklu Rapor Talebi] --> B[Sayfalama Parametrelerini Al]
    B --> C[Filtreleme Kriterlerini Uygula]
    C --> D[Akademisyen Listesini Getir]
    D --> E{Her Akademisyen İçin}
    E --> F[Bireysel Performans Hesapla]
    F --> G[Sonuçları Topla]
    G --> H{Daha Var mı?}
    H -->|Evet| E
    H -->|Hayır| I[Sıralama Uygula]
    I --> J[Sayfalama Uygula]
    J --> K[Toplam Sayıyı Hesapla]
    K --> L[Sayfalanmış Sonucu Döndür]
```

### 2.3 Rapor Export İşlemi

```mermaid
flowchart TD
    A[Export Talebi] --> B[Format Kontrolü]
    B --> C{PDF/Excel/CSV?}
    C -->|PDF| D[PDF Generator]
    C -->|Excel| E[Excel Generator]
    C -->|CSV| F[CSV Generator]
    D --> G[Dosya Oluştur]
    E --> G
    F --> G
    G --> H[Dosyayı Kaydet]
    H --> I[Export ID Oluştur]
    I --> J[Metadata Kaydet]
    J --> K[Export Sonucunu Döndür]
```

---

## 3. Veri Akışı (Data Flow)

### 3.1 Veri Toplama ve İşleme Akışı

```mermaid
flowchart LR
    A[Akademik Gönderimler] --> B[Form Kategorileri]
    B --> C[Kriter Bağlantıları]
    C --> D[Performans Hesaplama]
    D --> E[Kategori Skorları]
    E --> F[Genel Performans]
    F --> G[Rapor Oluşturma]
    
    H[Akademisyen Profilleri] --> D
    I[Feedback Verileri] --> D
    J[Zaman Filtreleri] --> D
```

### 3.2 Veri Dönüşüm Süreci

**Ham Veri → İşlenmiş Veri → Rapor**

1. **Ham Veri Toplama**:
   - AcademicSubmissions tablosundan gönderim verileri
   - EvaluationForm tablosundan form yapıları
   - FormCategory tablosundan kategori bilgileri
   - FormCriterionLink tablosundan kriter bağlantıları

2. **Veri İşleme**:
   - Tarih aralığına göre filtreleme
   - Kategori bazında gruplama
   - Kriter tamamlanma durumu hesaplama
   - Ağırlıklı skor hesaplamaları

3. **Rapor Formatı**:
   - PerformanceReportDto yapısına dönüştürme
   - Kategori ve kriter detaylarının eklenmesi
   - İstatistiksel analizlerin dahil edilmesi

---

## 4. Hesaplama Metodolojileri

### 4.1 Kategori Performans Skoru Hesaplama

**Formül:**
```
Kategori Skoru = (Tamamlanan Kriter Sayısı / Toplam Kriter Sayısı) × 100
Ağırlıklı Kategori Skoru = Kategori Skoru × Kategori Ağırlığı
```

**Hesaplama Adımları:**
1. Kategoriye bağlı tüm kriterleri getir
2. Tamamlanan (IsRequired=true) kriterleri say
3. Tamamlanma oranını hesapla
4. Kategori ağırlığını uygula

### 4.2 Genel Performans Skoru Hesaplama

**Formül:**
```
Genel Performans Skoru = Σ(Kategori Skorları) / Kategori Sayısı
```

**Alternatif Ağırlıklı Hesaplama:**
```
Ağırlıklı Genel Skor = Σ(Kategori Skoru × Kategori Ağırlığı) / Σ(Kategori Ağırlıkları)
```

### 4.3 Performans Seviyesi Belirleme

**Seviye Kriterleri:**
- **Excellent (Mükemmel)**: 90-100 puan
- **Good (İyi)**: 70-89 puan  
- **Average (Orta)**: 50-69 puan
- **Poor (Zayıf)**: 0-49 puan

### 4.4 Tamamlanma Oranı Hesaplama

**Formül:**
```
Tamamlanma Oranı = (Tamamlanan Form Sayısı / Toplam Form Sayısı) × 100
```

### 4.5 Ortalama Tamamlanma Süresi

**Hesaplama:**
```
Ortalama Süre = Σ(Form Tamamlanma Süreleri) / Tamamlanan Form Sayısı
```

---

## 5. Rapor Türleri

### 5.1 Akademisyen Performans Raporu

**İçerik:**
- Genel performans skoru ve seviyesi
- Kategori bazında detaylı skorlar
- Kriter bazında tamamlanma durumu
- Form tamamlanma istatistikleri
- Feedback analizi
- Zaman bazında trend analizi

**Kullanım Alanları:**
- Bireysel performans değerlendirme
- Gelişim alanlarının belirlenmesi
- Terfi ve ödül kararları

### 5.2 Çoklu Akademisyen Raporu

**İçerik:**
- Sayfalanmış akademisyen listesi
- Karşılaştırmalı performans skorları
- Sıralama ve filtreleme seçenekleri
- Toplu istatistikler

**Kullanım Alanları:**
- Bölüm bazında performans karşılaştırması
- Ranking ve sıralama işlemleri
- Toplu değerlendirmeler

### 5.3 Detaylı Akademisyen Raporu

**İçerik:**
- Kapsamlı performans analizi
- Kategori detay analizleri
- Kriter bazında detaylı performans
- Feedback detayları
- Zaman bazında performans trendi
- Karşılaştırmalı analiz (bölüm ortalaması ile)

**Kullanım Alanları:**
- Derinlemesine performans analizi
- Gelişim planlaması
- Mentoring ve koçluk süreçleri

### 5.4 Bölüm Performans Raporu

**İçerik:**
- Bölüm geneli performans ortalamaları
- Kategori bazında bölüm analizi
- Akademisyen dağılım istatistikleri
- Performans trend analizi

**Kullanım Alanları:**
- Bölüm yönetimi kararları
- Kaynak tahsisi planlaması
- Stratejik planlama

### 5.5 İstatistik ve Analiz Raporları

**Türler:**
- **KPI Metrikleri**: Anahtar performans göstergeleri
- **Benchmark Analizi**: Karşılaştırmalı performans
- **Trend Analizi**: Zaman bazında değişim
- **Karşılaştırma Analizi**: Çoklu boyutlu karşılaştırmalar

---

## 6. Kullanıcı Rolleri ve Yetkilendirme

### 6.1 Yetkilendirme Sistemi

Sistem, claim-based authorization kullanarak granüler yetki kontrolü sağlar.

**Ana Yetki Türleri:**
- `permission.action.ap.accessreporting`: Raporlama sistemine erişim
- `permission.action.ap.viewreports`: Rapor görüntüleme
- `permission.action.ap.manageforms`: Form yönetimi
- `permission.action.ap.approvesubmissions`: Gönderim onaylama

### 6.2 Kullanıcı Rolleri

#### 6.2.1 Sistem Yöneticileri (SuperAdmin/Admin)
- **Yetkiler**: Tüm sistem fonksiyonlarına erişim
- **Sorumluluklar**: Sistem konfigürasyonu, kullanıcı yönetimi
- **Raporlama Yetkileri**: Tüm raporlara erişim, sistem geneli analiz

#### 6.2.2 Bölüm Yöneticileri (Department Managers)
- **Yetkiler**: Bölüm personeli raporlarına erişim
- **Sorumluluklar**: Bölüm performansının izlenmesi
- **Raporlama Yetkileri**: Bölüm bazında tüm raporlar, karşılaştırmalı analizler

#### 6.2.3 Raporlama Kullanıcıları (Report Viewers)
- **Yetkiler**: Belirli rapor türlerine erişim
- **Sorumluluklar**: Veri analizi ve raporlama
- **Raporlama Yetkileri**: Performans raporları, istatistiksel analizler

#### 6.2.4 Akademisyenler (Self-View)
- **Yetkiler**: Kendi performans raporlarını görüntüleme
- **Sorumluluklar**: Kişisel gelişim takibi
- **Raporlama Yetkileri**: Bireysel performans raporu, trend analizi

### 6.3 Yetki Kontrolü Mekanizması

```mermaid
flowchart TD
    A[API İsteği] --> B[Authentication Kontrolü]
    B --> C{Authenticated?}
    C -->|Hayır| D[401 Unauthorized]
    C -->|Evet| E[Policy Kontrolü]
    E --> F{AccessReporting Policy?}
    F -->|Hayır| G[403 Forbidden]
    F -->|Evet| H[Admin Bypass Kontrolü]
    H --> I{Admin Role?}
    I -->|Evet| J[Tam Erişim]
    I -->|Hayır| K[Spesifik Yetki Kontrolü]
    K --> L{Gerekli Claim Var?}
    L -->|Hayır| G
    L -->|Evet| M[İşlem İzni Ver]
```

---

## 7. Performans ve Optimizasyon

### 7.1 Cache Stratejileri

#### 7.1.1 Performans Raporu Cache
- **Cache Key**: `performance_report:{academicianId}:{period}`
- **TTL**: 30 dakika
- **Kullanım**: Sık erişilen bireysel performans raporları

#### 7.1.2 İstatistik Cache
- **Cache Key**: `performance_stats:{departmentId}:{period}`
- **TTL**: 60 dakika
- **Kullanım**: Bölüm bazında istatistikler

#### 7.1.3 Rapor Cache
- **Cache Key**: `report_cache:{reportType}:{scopeId}:{period}`
- **TTL**: 120 dakika
- **Kullanım**: Karmaşık raporlar ve analizler

### 7.2 Veri Yönetimi Optimizasyonları

#### 7.2.1 Veritabanı Optimizasyonları
- **Indexleme**: Sık sorgulanan alanlarda index kullanımı
- **Eager Loading**: İlişkili verilerin tek sorguda getirilmesi
- **Pagination**: Büyük veri setlerinde sayfalama

#### 7.2.2 Sorgu Optimizasyonları
- **Filtreleme**: Erken filtreleme ile veri miktarının azaltılması
- **Projection**: Sadece gerekli alanların seçilmesi
- **Grouping**: Veritabanı seviyesinde gruplama işlemleri

### 7.3 Performans Metrikleri

#### 7.3.1 Yanıt Süreleri
- **Bireysel Rapor**: < 2 saniye
- **Çoklu Rapor**: < 5 saniye
- **Export İşlemleri**: < 30 saniye

#### 7.3.2 Cache Hit Oranları
- **Hedef**: %80+ cache hit oranı
- **İzleme**: Gerçek zamanlı cache performans metrikleri

#### 7.3.3 Sistem Kaynak Kullanımı
- **Memory**: Optimal bellek kullanımı
- **CPU**: Hesaplama yoğun işlemlerde CPU optimizasyonu
- **I/O**: Veritabanı bağlantı havuzu yönetimi

---

## 8. Güvenlik

### 8.1 Kimlik Doğrulama
- **OpenIddict**: JWT token tabanlı kimlik doğrulama
- **Token Validation**: Her istekte token geçerliliği kontrolü
- **Encryption**: Sertifika tabanlı şifreleme

### 8.2 Yetkilendirme
- **Policy-Based**: Granüler yetki kontrolü
- **Claim-Based**: Kullanıcı claim'lerine dayalı erişim
- **Role-Based**: Rol tabanlı yetki yönetimi

### 8.3 Veri Güvenliği
- **Data Masking**: Hassas verilerin maskelenmesi
- **Audit Logging**: Tüm işlemlerin loglanması
- **Access Control**: Veri erişim kontrolü

### 8.4 API Güvenliği
- **HTTPS**: Tüm iletişimde SSL/TLS
- **Rate Limiting**: İstek sınırlaması
- **Input Validation**: Giriş verilerinin doğrulanması
